package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"ai-text-game-iam-npc/internal/ai"
	"ai-text-game-iam-npc/internal/auth"
	"ai-text-game-iam-npc/internal/testutil"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// MockAIService 模拟AI服务
type MockAIService struct {
	generateContentFunc func(ctx context.Context, req *ai.GenerateRequest) (*ai.GenerateResponse, error)
}

func (m *MockAIService) GenerateContent(ctx context.Context, req *ai.GenerateRequest) (*ai.GenerateResponse, error) {
	if m.generateContentFunc != nil {
		return m.generateContentFunc(ctx, req)
	}
	return &ai.GenerateResponse{
		Content:        "生成的场景描述",
		StructuredData: map[string]interface{}{
			"name":        "测试场景",
			"description": "这是一个测试生成的场景",
			"atmosphere":  "神秘",
			"type":        "forest",
		},
		TokenUsage: 150,
	}, nil
}

func (m *MockAIService) GetInteractionHistory(worldID *uuid.UUID, userID *uuid.UUID, limit, offset int) ([]ai.InteractionRecord, int64, error) {
	return []ai.InteractionRecord{}, 0, nil
}

func (m *MockAIService) GetTokenUsageStats(worldID *uuid.UUID, userID *uuid.UUID, days int) (*ai.TokenUsageStats, error) {
	return &ai.TokenUsageStats{}, nil
}

// setupAIHandler 设置AI处理器测试环境
func setupAIHandler(t *testing.T) (*AIHandler, *gin.Engine) {
	gin.SetMode(gin.TestMode)
	
	mockAIService := &MockAIService{}
	handler := NewAIHandler(mockAIService)
	
	router := gin.New()
	router.Use(func(c *gin.Context) {
		// 模拟认证中间件，设置测试用户ID
		testUserID := uuid.New()
		c.Set(auth.UserIDKey, testUserID)
		c.Next()
	})
	
	return handler, router
}

func TestAIHandler_GenerateScene(t *testing.T) {
	handler, router := setupAIHandler(t)
	router.POST("/ai/generate/scene", handler.GenerateScene)

	tests := []struct {
		name           string
		requestBody    GenerateSceneRequest
		expectedStatus int
		expectedData   bool
		description    string
	}{
		{
			name: "成功生成场景_新格式",
			requestBody: GenerateSceneRequest{
				WorldID:             "test-world-id",
				SceneName:           "神秘森林",
				SceneType:           "main",
				Theme:               "fantasy",
				Mood:                "mysterious",
				SpecialRequirements: "包含古老的遗迹",
			},
			expectedStatus: http.StatusOK,
			expectedData:   true,
			description:    "使用新格式参数成功生成场景",
		},
		{
			name: "成功生成场景_兼容旧格式",
			requestBody: GenerateSceneRequest{
				WorldID: "test-world-id",
				Prompt:  "生成一个神秘的森林场景",
				Context: map[string]interface{}{
					"theme": "fantasy",
					"mood":  "dark",
				},
			},
			expectedStatus: http.StatusOK,
			expectedData:   true,
			description:    "使用旧格式参数成功生成场景",
		},
		{
			name: "缺少世界ID",
			requestBody: GenerateSceneRequest{
				SceneName: "测试场景",
			},
			expectedStatus: http.StatusBadRequest,
			expectedData:   false,
			description:    "缺少必需的世界ID参数",
		},
		{
			name: "临时世界ID",
			requestBody: GenerateSceneRequest{
				WorldID:   "temp",
				SceneName: "临时场景",
				Theme:     "fantasy",
			},
			expectedStatus: http.StatusOK,
			expectedData:   true,
			description:    "使用临时世界ID生成场景",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备请求体
			requestBody, err := json.Marshal(tt.requestBody)
			require.NoError(t, err, "序列化请求体失败")

			// 创建HTTP请求
			req, err := http.NewRequest("POST", "/ai/generate/scene", bytes.NewBuffer(requestBody))
			require.NoError(t, err, "创建HTTP请求失败")
			req.Header.Set("Content-Type", "application/json")

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应状态码
			assert.Equal(t, tt.expectedStatus, w.Code, tt.description)

			// 解析响应体
			var response Response
			err = json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err, "解析响应体失败")

			if tt.expectedData {
				// 验证成功响应
				assert.True(t, response.Success, "响应应该标记为成功")
				assert.NotNil(t, response.Data, "响应数据不应该为空")
				assert.Contains(t, response.Message, "成功", "响应消息应该包含成功信息")
			} else {
				// 验证错误响应
				assert.False(t, response.Success, "响应应该标记为失败")
				assert.NotEmpty(t, response.Message, "错误消息不应该为空")
			}
		})
	}
}

func TestAIHandler_BuildScenePrompt(t *testing.T) {
	handler, _ := setupAIHandler(t)

	tests := []struct {
		name        string
		request     GenerateSceneRequest
		expected    string
		description string
	}{
		{
			name: "完整参数构建提示词",
			request: GenerateSceneRequest{
				SceneName:           "神秘森林",
				SceneType:           "main",
				Theme:               "fantasy",
				Mood:                "dark",
				ConnectedScenes:     []string{"村庄", "山洞"},
				SpecialRequirements: "包含魔法元素",
			},
			expected:    "请生成一个游戏场景的详细描述。场景名称：神秘森林。主题风格：fantasy。场景类型：main。氛围设定：dark。特殊要求：包含魔法元素。需要连接的场景：村庄、山洞。请生成包含场景名称、详细描述、氛围、关键特征和可能的行动的完整场景信息。",
			description: "使用完整参数构建详细提示词",
		},
		{
			name: "使用旧格式提示词",
			request: GenerateSceneRequest{
				Prompt: "生成一个神秘的森林场景",
			},
			expected:    "生成一个神秘的森林场景",
			description: "直接使用旧格式的提示词",
		},
		{
			name: "最小参数构建提示词",
			request: GenerateSceneRequest{
				SceneName: "简单场景",
			},
			expected:    "请生成一个游戏场景的详细描述。场景名称：简单场景。请生成包含场景名称、详细描述、氛围、关键特征和可能的行动的完整场景信息。",
			description: "使用最小参数构建基础提示词",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.buildScenePrompt(tt.request)
			assert.Equal(t, tt.expected, result, tt.description)
		})
	}
}

func TestAIHandler_BuildSceneContext(t *testing.T) {
	handler, _ := setupAIHandler(t)

	tests := []struct {
		name        string
		request     GenerateSceneRequest
		expected    map[string]interface{}
		description string
	}{
		{
			name: "构建完整上下文",
			request: GenerateSceneRequest{
				WorldID:             "test-world",
				SceneName:           "测试场景",
				SceneType:           "special",
				Theme:               "sci-fi",
				Mood:                "tense",
				ConnectedScenes:     []string{"场景1", "场景2"},
				SpecialRequirements: "包含机器人",
			},
			expected: map[string]interface{}{
				"world_id":             "test-world",
				"scene_name":           "测试场景",
				"scene_type":           "special",
				"theme":                "sci-fi",
				"mood":                 "tense",
				"connected_scenes":     []string{"场景1", "场景2"},
				"special_requirements": "包含机器人",
			},
			description: "构建包含所有字段的上下文",
		},
		{
			name: "合并旧格式上下文",
			request: GenerateSceneRequest{
				WorldID:   "test-world",
				SceneName: "新场景",
				Context: map[string]interface{}{
					"existing_key": "existing_value",
					"theme":        "old_theme", // 应该被新值覆盖
				},
			},
			expected: map[string]interface{}{
				"world_id":     "test-world",
				"scene_name":   "新场景",
				"existing_key": "existing_value",
				"theme":        "old_theme", // 旧值保留，因为新格式中没有theme
			},
			description: "正确合并旧格式和新格式的上下文",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.buildSceneContext(tt.request)
			
			// 验证所有期望的键值对都存在
			for key, expectedValue := range tt.expected {
				actualValue, exists := result[key]
				assert.True(t, exists, "上下文应该包含键: %s", key)
				assert.Equal(t, expectedValue, actualValue, "键 %s 的值不匹配", key)
			}
		})
	}
}
